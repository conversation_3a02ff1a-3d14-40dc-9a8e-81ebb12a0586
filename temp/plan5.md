### **Development Plan: E4.S7 - Select Model & Settings Profile for Session**

**Feature:** Allow user to select the current model and settings profile for the next assistant response.
**User Story ID:** E4.S7
**Estimated Effort:** Medium

#### **1. Backend Verification (Already Complete)**
**Status:** ✅ **COMPLETE** - All backend components are already implemented and functional.
- The backend provides all necessary endpoints for session updates, and for fetching models and settings.

#### **2. Frontend API Clients Verification (Already Complete)**
**Status:** ✅ **COMPLETE** - All API client methods are implemented.

#### **3. Repository Layer Verification (Already Complete)**
**Status:** ✅ **COMPLETE** - All repository methods are implemented.

#### **4. ViewModel Layer Enhancement (Implementation Required)**
**Goal:** Extend `ChatState` and `ChatStateImpl` to expose all necessary data for the UI, including available models/settings, lookup maps, and the currently selected items, while ensuring a single source of truth.

##### **4.1 Update `ChatState` Interface**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatState.kt`

Update the interface to expose decoupled data states and derived, UI-optimized flows. This ensures UI components can react to precisely the data they need and handle their states independently.

```kotlin
interface ChatState {
    // --- Core Data States ---
    /**
     * The state of the currently loaded chat session.
     */
    val sessionDataState: StateFlow<DataState<RepositoryError, ChatSession>>
    /**
     * The list of all currently configured LLM models available for selection.
     */
    val availableModels: StateFlow<DataState<RepositoryError, List<LLMModel>>>
    /**
     * The list of settings profiles available for the currently selected model.
     */
    val availableSettingsForCurrentModel: StateFlow<DataState<RepositoryError, List<ModelSettings>>>

    // --- Derived Lookup Maps (for performance & graceful degradation) ---
    /**
     * A map of model IDs to LLMModel objects, derived from `availableModels`.
     * This is optimized for quick lookups (e.g., rendering message metadata).
     * It will be an empty map if models are loading or failed to load.
     */
    val modelsById: StateFlow<Map<Long, LLMModel>>
    /**
     * A map of settings IDs to ModelSettings objects, derived from the global settings list.
     * Optimized for quick lookups.
     */
    val settingsById: StateFlow<Map<Long, ModelSettings>>

    // --- Derived "Current Item" States (for UI convenience) ---
    /**
     * The currently active ChatSession object, or null if not loaded.
     * Derived from sessionDataState.
     */
    val currentSession: StateFlow<ChatSession?>
    /**
     * The fully resolved LLMModel object for the current session, or null.
     * Derived by combining currentSession and modelsById.
     */
    val currentModel: StateFlow<LLMModel?>
    /**
     * The fully resolved ModelSettings object for the current session, or null.
     * Derived by combining currentSession and settingsById.
     */
    val currentSettings: StateFlow<ModelSettings?>

    // ... existing properties and methods ...
}
```

##### **4.2 Implement in `ChatStateImpl`**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatStateImpl.kt`

Implement the new reactive flows. The logic ensures data is normalized (no duplication) and state is derived efficiently from a few core sources.

```kotlin
@OptIn(ExperimentalCoroutinesApi::class)
class ChatStateImpl(
    private val sessionRepository: SessionRepository,
    private val settingsRepository: SettingsRepository,
    private val modelRepository: ModelRepository,
    // ...
) : ChatState {
    // --- Existing private flows for user input (_activeSessionId, etc.) ---
    // ...

    // --- Core Reactive State Derivation ---
    override val sessionDataState: StateFlow<DataState<RepositoryError, ChatSession>> =
        _activeSessionId.flatMapLatest { id ->
            if (id == null) flowOf(DataState.Idle)
            else sessionRepository.getSessionDetailsFlow(id)
        }.stateIn(
            scope = backgroundScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = DataState.Idle
        )

    override val availableModels: StateFlow<DataState<RepositoryError, List<LLMModel>>> =
        modelRepository.models.stateIn(
            scope = backgroundScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = DataState.Idle
        )

    private val allSettings: StateFlow<DataState<RepositoryError, List<ModelSettings>>> =
        settingsRepository.settings.stateIn(
            scope = backgroundScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = DataState.Idle
        )

    // --- Derived Lookup Maps ---
    override val modelsById: StateFlow<Map<Long, LLMModel>> =
        availableModels.map { it.dataOrNull?.associateBy { model -> model.id } ?: emptyMap() }
            .stateIn(backgroundScope, SharingStarted.WhileSubscribed(5000), emptyMap())

    override val settingsById: StateFlow<Map<Long, ModelSettings>> =
        allSettings.map { it.dataOrNull?.associateBy { settings -> settings.id } ?: emptyMap() }
            .stateIn(backgroundScope, SharingStarted.WhileSubscribed(5000), emptyMap())

    // --- Derived "Current Item" States ---
    override val currentSession: StateFlow<ChatSession?> =
        sessionDataState.map { it.dataOrNull }
            .stateIn(backgroundScope, SharingStarted.WhileSubscribed(5000), null)

    override val currentModel: StateFlow<LLMModel?> =
        combine(currentSession, modelsById) { session, modelsMap ->
            session?.currentModelId?.let { modelsMap[it] }
        }.stateIn(backgroundScope, SharingStarted.WhileSubscribed(5000), null)

    override val currentSettings: StateFlow<ModelSettings?> =
        combine(currentSession, settingsById) { session, settingsMap ->
            session?.currentSettingsId?.let { settingsMap[it] }
        }.stateIn(backgroundScope, SharingStarted.WhileSubscribed(5000), null)

    // --- Derived Filtered List for UI ---
    override val availableSettingsForCurrentModel: StateFlow<DataState<RepositoryError, List<ModelSettings>>> =
        combine(currentModel, allSettings) { model, settingsState ->
            val currentModelId = model?.id
            when (settingsState) {
                is DataState.Success -> {
                    val filtered = if (currentModelId != null) {
                        settingsState.data.filter { it.modelId == currentModelId }
                    } else {
                        emptyList()
                    }
                    DataState.Success(filtered)
                }
                // Pass through Error, Loading, Idle states directly
                is DataState.Error -> settingsState
                is DataState.Loading -> DataState.Loading
                is DataState.Idle -> DataState.Idle
            }
        }.stateIn(backgroundScope, SharingStarted.WhileSubscribed(5000), DataState.Idle)

    // ... rest of implementation ...
}
```

##### **4.3 Remove `ChatSessionData`**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatSessionData.kt`
This wrapper class is no longer necessary with the new normalized state. It should be deleted.

#### **5. UI State Enhancement (Implementation Required)**

##### **5.1 Update `ChatAreaState`**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatAreaState.kt`
Update the `sessionUiState` to hold `ChatSession` directly and add the `DataState` flows needed by the `ModelSettingsSelector`.

```kotlin
data class ChatAreaState(
    val sessionUiState: DataState<RepositoryError, ChatSession> = DataState.Idle,
    val availableModels: DataState<RepositoryError, List<LLMModel>> = DataState.Idle,
    val availableSettingsForCurrentModel: DataState<RepositoryError, List<ModelSettings>> = DataState.Idle,
    // ... existing properties ...
)
```

##### **5.2 Update `ChatScreen` State Collection**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatScreen.kt`
Collect all necessary states from the ViewModel. The UI will pass the resolved `currentModel` and `currentSettings` to the selector, while the `DataState`s are used to control its loading/error UI.

```kotlin
// In ChatScreen composable
val sessionState by viewModel.sessionDataState.collectAsState()
val availableModels by viewModel.availableModels.collectAsState()
val availableSettings by viewModel.availableSettingsForCurrentModel.collectAsState()

val currentModel by viewModel.currentModel.collectAsState()
val currentSettings by viewModel.currentSettings.collectAsState()
val modelsById by viewModel.modelsById.collectAsState()

val chatAreaState = remember(sessionState, availableModels, availableSettings, /*...*/) {
    ChatAreaState(
        sessionUiState = sessionState,
        availableModels = availableModels,
        availableSettingsForCurrentModel = availableSettings,
        // ... existing properties ...
    )
}

ChatArea(
    state = chatAreaState,
    actions = chatAreaActions,
    currentModel = currentModel,
    currentSettings = currentSettings,
    modelsById = modelsById
)
```

#### **6. UI Component Implementation (Implementation Required)**

##### **6.1 Create `ModelSettingsSelector` Component**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ModelSettingsSelector.kt`

Create a new, resilient Composable that handles its own loading and error states internally. This prevents failures in loading model/settings lists from breaking the main chat UI.

```kotlin
// (Full implementation as provided in the previous response)
// Includes the main ModelSettingsSelector, the SingleSelector helper,
// and the SelectorDropdown helper. Key features:
// - Takes DataState<> for models and settings.
// - Displays inline loading indicators.
// - Displays inline error messages with retry buttons.
// - Exposes onRetry callbacks.
@Composable
fun ModelSettingsSelector(
    currentModel: LLMModel?,
    currentSettings: ModelSettings?,
    availableModels: DataState<RepositoryError, List<LLMModel>>,
    availableSettings: DataState<RepositoryError, List<ModelSettings>>,
    onSelectModel: (Long) -> Unit,
    onSelectSettings: (Long) -> Unit,
    onRetryLoadModels: () -> Unit,
    onRetryLoadSettings: () -> Unit,
    modifier: Modifier = Modifier
) {
    // ... Full implementation with Row, SingleSelector, etc.
}
```

##### **6.2 Integrate into `ChatArea`**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatArea.kt`

The `ChatArea` will now accept the new props and pass them down to its children.

```kotlin
@Composable
fun ChatArea(
    state: ChatAreaState,
    actions: ChatAreaActions,
    currentModel: LLMModel?,
    currentSettings: ModelSettings?,
    modelsById: Map<Long, LLMModel>
) {
    // ...
    when (state.sessionUiState) {
        // ...
        is DataState.Success -> SuccessStateDisplay(
            chatSession = state.sessionUiState.data,
            // Pass new props down
            currentModel = currentModel,
            currentSettings = currentSettings,
            availableModels = state.availableModels,
            availableSettingsForCurrentModel = state.availableSettingsForCurrentModel,
            modelsById = modelsById,
            // ...
        )
    }
}

@Composable
private fun SuccessStateDisplay(
    chatSession: ChatSession,
    currentModel: LLMModel?,
    currentSettings: ModelSettings?,
    availableModels: DataState<RepositoryError, List<LLMModel>>,
    availableSettingsForCurrentModel: DataState<RepositoryError, List<ModelSettings>>,
    modelsById: Map<Long, LLMModel>,
    //...
) {
    Column(modifier = Modifier.fillMaxSize()) {
        ModelSettingsSelector(
            currentModel = currentModel,
            currentSettings = currentSettings,
            availableModels = availableModels,
            availableSettings = availableSettingsForCurrentModel,
            onSelectModel = { modelId -> actions.onSelectModel(modelId) },
            onSelectSettings = { settingsId -> actions.onSelectSettings(settingsId) },
            onRetryLoadModels = { /* TODO: Wire up to ViewModel action */ },
            onRetryLoadSettings = { /* TODO: Wire up to ViewModel action */ },
            modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp, vertical = 8.dp)
        )
        
        MessageList(
            chatSession = chatSession,
            modelsById = modelsById, // Pass map for graceful degradation
            // ...
        )
        
        InputArea(/* ... */)
    }
}
```
*(Note: `MessageList` and its children will need to be updated to accept `modelsById` to render model names on assistant messages.)*

#### **7. Data Loading Enhancement (Implementation Required)**
**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/usecase/LoadSessionUseCase.kt`

Refactor the use case to orchestrate the loading of all data required for the screen. The session load is critical; model/settings loads are non-critical and their states are handled by the UI.

```kotlin
class LoadSessionUseCase(
    private val sessionRepository: SessionRepository,
    private val settingsRepository: SettingsRepository,
    private val modelRepository: ModelRepository,
    // ...
) {
    suspend fun execute(sessionId: Long, forceReload: Boolean = false) {
        // ... (guard clauses)
        state.setRetryState(sessionId, null)

        // Trigger non-critical background loads. The UI will observe their
        // respective StateFlows and update from Loading -> Success/Error.
        // The main session load does not wait for these and will not fail if they do.
        modelRepository.loadModels()
        settingsRepository.loadSettings()

        // CRITICAL DATA LOAD: The session itself.
        val sessionResult = sessionRepository.loadSessionDetails(sessionId)

        sessionResult.fold(
            ifLeft = { repositoryError ->
                // A critical failure. The session could not be displayed.
                val eventId = errorNotifier.repositoryError(
                    error = repositoryError,
                    shortMessageRes = Res.string.error_loading_session,
                    isRetryable = true
                )
                state.setRetryState(sessionId, eventId)
            },
            ifRight = { session ->
                // Critical data loaded successfully.
                logger.info("Successfully loaded session $sessionId. Dependencies are loading in background.")
                state.resetState()
                state.setActiveSessionId(sessionId)
            }
        )
    }
    // ...
}
```

#### **8. Testing Requirements**
-   **Unit Tests (`ChatStateImpl`):**
    -   Verify that `modelsById` and `settingsById` correctly transform a `Success` state into a map.
    -   Verify that `modelsById` and `settingsById` are empty for `Loading`/`Error`/`Idle` states.
    -   Verify that `currentModel` and `currentSettings` correctly resolve objects when the session and maps are available.
    -   Verify that `currentModel` is null if the session's `currentModelId` is not in the map.
-   **UI Tests (`ModelSettingsSelector`):**
    -   Create `@Preview` tests for all states: Success, Loading, Error, and mixed states.
    -   Write interaction tests to verify that `onRetry` callbacks are triggered when the retry button is clicked.
-   **UI Tests (Message List):**
    -   Verify that an assistant message correctly displays the model's display name when `modelsById` is populated.
    -   Verify **graceful degradation**: an assistant message displays a fallback (e.g., "Model ID: 123") when `modelsById` is empty or does not contain the required ID.

#### **9. Implementation Order**
1.  **Refactor State:** Delete `ChatSessionData` and update `ChatState` interface.
2.  **Implement State Logic:** Implement the new derived flows in `ChatStateImpl`.
3.  **Update Loading Logic:** Refactor `LoadSessionUseCase`.
4.  **Update UI State Models:** Modify `ChatAreaState`.
5.  **Build UI Component:** Implement the `ModelSettingsSelector` composable with all its states.
6.  **Integrate:** Update `ChatScreen` and `ChatArea` to wire everything together. Update `MessageList` to use `modelsById`.
7.  **Add Tests:** Write unit and UI tests for the new logic and components.

#### **10. Architectural Notes**
-   **Single Source of Truth:** The architecture avoids data duplication. `ChatSession` holds IDs, and repositories are the single source for the full data objects. This prevents stale data.
-   **Decoupled UI States:** The state of the session, the model list, and the settings list are independent. A failure in one does not cause a failure in the others.
-   **Graceful Degradation:** The UI is designed to be resilient. If non-critical data (like the model name for an old message) fails to load, the UI displays a sensible fallback instead of crashing or showing a full-screen error. This provides a superior user experience.


---

### **Full Implementation of `ModelSettingsSelector`**

Here is a full, production-quality implementation of the `ModelSettingsSelector` composable. It is designed to be robust, self-contained, and provide a great user experience by handling all data states gracefully within its own boundaries.

This implementation includes:
1.  **The main `ModelSettingsSelector` Composable.**
2.  **A reusable `SingleSelector` helper** to avoid code duplication for the model and settings dropdowns.
3.  **Comprehensive `@Preview` functions** that allow you to see exactly how it looks in all states (`Success`, `Loading`, `Error`, and a mixed state) directly in your IDE.

---

### File: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ModelSettingsSelector.kt`

```kotlin
package eu.torvian.chatbot.app.compose.chatarea

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import org.jetbrains.compose.ui.tooling.preview.Preview

/**
 * A composite UI component for selecting the LLM model and its corresponding settings profile.
 *
 * This component is designed to be resilient and handles its own internal states for loading and errors
 * for both models and settings lists independently. This prevents a failure in loading, for example,
 * the settings list from disrupting the model selection or the main chat view.
 *
 * @param currentModel The currently selected LLM model.
 * @param currentSettings The currently selected settings profile.
 * @param availableModels The state of the list of all available models.
 * @param availableSettings The state of the list of settings available for the current model.
 * @param onSelectModel Callback triggered when a user selects a model from the dropdown.
 * @param onSelectSettings Callback triggered when a user selects a settings profile.
 * @param onRetryLoadModels Callback to retry loading the model list if it failed.
 * @param onRetryLoadSettings Callback to retry loading the settings list if it failed.
 * @param modifier The modifier to be applied to the component.
 */
@Composable
fun ModelSettingsSelector(
    currentModel: LLMModel?,
    currentSettings: ModelSettings?,
    availableModels: DataState<RepositoryError, List<LLMModel>>,
    availableSettings: DataState<RepositoryError, List<ModelSettings>>,
    onSelectModel: (Long) -> Unit,
    onSelectSettings: (Long) -> Unit,
    onRetryLoadModels: () -> Unit,
    onRetryLoadSettings: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.Top
    ) {
        // --- Model Dropdown ---
        SingleSelector(
            label = "Model",
            dataState = availableModels,
            currentValueDisplay = currentModel?.name ?: "Select a Model",
            onRetry = onRetryLoadModels,
            modifier = Modifier.weight(1f)
        ) { models ->
            var expanded by remember { mutableStateOf(false) }
            SelectorDropdown(
                expanded = expanded,
                onExpandedChange = { expanded = it },
                currentValueDisplay = currentModel?.name ?: "Select a Model",
                items = models,
                itemLabel = { it.name },
                onItemSelected = {
                    onSelectModel(it.id)
                    expanded = false
                }
            )
        }

        // --- Settings Dropdown ---
        SingleSelector(
            label = "Settings Profile",
            dataState = availableSettings,
            currentValueDisplay = currentSettings?.name ?: "Default",
            onRetry = onRetryLoadSettings,
            modifier = Modifier.weight(1f)
        ) { settings ->
            var expanded by remember { mutableStateOf(false) }
            SelectorDropdown(
                expanded = expanded,
                onExpandedChange = { expanded = it },
                currentValueDisplay = currentSettings?.name ?: "Default",
                items = settings,
                itemLabel = { it.name },
                onItemSelected = {
                    onSelectSettings(it.id)
                    expanded = false
                }
            )
        }
    }
}

/**
 * A reusable helper composable that encapsulates the logic for displaying a
 * selector in its Loading, Error, or Success state.
 *
 * @param T The type of data in the list.
 * @param label The text label to display above the selector.
 * @param dataState The DataState object that drives the UI.
 * @param currentValueDisplay The string representation of the currently selected value.
 * @param onRetry The callback to invoke when the retry button is clicked in the error state.
 * @param modifier The modifier for the component.
 * @param successContent The composable lambda to render when the dataState is Success. It receives the loaded data list.
 */
@Composable
private fun <T> SingleSelector(
    label: String,
    dataState: DataState<RepositoryError, List<T>>,
    currentValueDisplay: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier,
    successContent: @Composable (data: List<T>) -> Unit
) {
    Column(modifier = modifier) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(Modifier.height(4.dp))
        Box(
            modifier = Modifier.fillMaxWidth().height(48.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            when (dataState) {
                is DataState.Idle, is DataState.Loading -> {
                    CircularProgressIndicator(modifier = Modifier.size(24.dp).align(Alignment.Center))
                }

                is DataState.Error -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            "Load failed",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error,
                            modifier = Modifier.padding(start = 12.dp)
                        )
                        IconButton(onClick = onRetry) {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = "Retry loading $label",
                                tint = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }

                is DataState.Success -> {
                    successContent(dataState.data)
                }
            }
        }
    }
}


/**
 * A generic dropdown menu component.
 *
 * @param T The type of item in the list.
 * @param expanded Whether the dropdown menu is currently visible.
 * @param onExpandedChange Callback to change the expanded state.
 * @param currentValueDisplay The text to display on the button.
 * @param items The list of items to display in the dropdown.
 * @param itemLabel A function to get the string label for an item.
 * @param onItemSelected The callback invoked when an item is selected.
 */
@Composable
private fun <T> SelectorDropdown(
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    currentValueDisplay: String,
    items: List<T>,
    itemLabel: (T) -> String,
    onItemSelected: (T) -> Unit
) {
    Box {
        OutlinedButton(
            onClick = { onExpandedChange(true) },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = currentValueDisplay,
                modifier = Modifier.weight(1f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(Modifier.width(8.dp))
            Icon(Icons.Default.ArrowDropDown, contentDescription = "Open dropdown")
        }
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { onExpandedChange(false) },
            modifier = Modifier.fillMaxWidth(0.4f) // Adjust width as needed
        ) {
            items.forEach { item ->
                DropdownMenuItem(
                    text = { Text(itemLabel(item)) },
                    onClick = { onItemSelected(item) }
                )
            }
        }
    }
}

// --- Previews for different states ---

@Preview
@Composable
private fun ModelSettingsSelector_SuccessState_Preview() {
    val models = listOf(
        LLMModel(1, "GPT-4 Turbo", "openai", "gpt-4-1106-preview"),
        LLMModel(2, "Claude 3 Sonnet", "anthropic", "claude-3-sonnet-20240229")
    )
    val settings = listOf(
        ModelSettings(10, 1, "Creative", "Default creative profile"),
        ModelSettings(11, 1, "Balanced", "Default balanced profile")
    )
    MaterialTheme {
        ModelSettingsSelector(
            currentModel = models.first(),
            currentSettings = settings.last(),
            availableModels = DataState.Success(models),
            availableSettings = DataState.Success(settings),
            onSelectModel = {},
            onSelectSettings = {},
            onRetryLoadModels = {},
            onRetryLoadSettings = {},
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview
@Composable
private fun ModelSettingsSelector_LoadingState_Preview() {
    MaterialTheme {
        ModelSettingsSelector(
            currentModel = null,
            currentSettings = null,
            availableModels = DataState.Loading,
            availableSettings = DataState.Loading,
            onSelectModel = {},
            onSelectSettings = {},
            onRetryLoadModels = {},
            onRetryLoadSettings = {},
            modifier = Modifier.padding(16.dp)
        )
    }
}


@Preview
@Composable
private fun ModelSettingsSelector_ErrorState_Preview() {
    MaterialTheme {
        ModelSettingsSelector(
            currentModel = null,
            currentSettings = null,
            availableModels = DataState.Error(RepositoryError.OtherError("Network timeout")),
            availableSettings = DataState.Error(RepositoryError.OtherError("Server error")),
            onSelectModel = {},
            onSelectSettings = {},
            onRetryLoadModels = {},
            onRetryLoadSettings = {},
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview
@Composable
private fun ModelSettingsSelector_MixedState_Preview() {
    val models = listOf(
        LLMModel(1, "GPT-4 Turbo", "openai", "gpt-4-1106-preview")
    )
    MaterialTheme {
        ModelSettingsSelector(
            currentModel = models.first(),
            currentSettings = null,
            availableModels = DataState.Success(models), // Models loaded
            availableSettings = DataState.Error(RepositoryError.OtherError("Not found")), // Settings failed
            onSelectModel = {},
            onSelectSettings = {},
            onRetryLoadModels = {},
            onRetryLoadSettings = {},
            modifier = Modifier.padding(16.dp)
        )
    }
}
```