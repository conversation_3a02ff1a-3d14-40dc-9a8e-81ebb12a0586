## Development Plan: E4.S7 - Select Model & Settings Profile for Session

**Feature:** Allow user to select the current model and settings profile for the next assistant response.
**User Story ID:** E4.S7
**Estimated Effort:** Medium

### 1. Backend Verification (Already Complete)

**Status:** ✅ **COMPLETE** - All backend components are already implemented and functional.

The backend already provides:
- ✅ `PUT /api/v1/sessions/{sessionId}/model` - Updates session's current model ID
- ✅ `PUT /api/v1/sessions/{sessionId}/settings` - Updates session's current settings ID  
- ✅ `GET /api/v1/models` - Retrieves all LLM models
- ✅ `GET /api/v1/models/{modelId}/settings` - Retrieves settings for a specific model
- ✅ `GET /api/v1/settings` - Retrieves all settings profiles
- ✅ Database schema with proper foreign keys and CASCADE behavior
- ✅ Service layer implementations (`SessionService`, `LLMModelService`, `ModelSettingsService`)

### 2. Frontend API Clients Verification (Already Complete)

**Status:** ✅ **COMPLETE** - All API client methods are implemented.

The frontend API clients already provide:
- ✅ `KtorSessionApiClient.updateSessionModel()` and `updateSessionSettings()`
- ✅ `KtorModelApiClient.getAllModels()`
- ✅ `KtorSettingsApiClient.getSettingsByModelId()` and `getAllSettings()`

### 3. Repository Layer Verification (Already Complete)

**Status:** ✅ **COMPLETE** - All repository methods are implemented.

The repositories already provide:
- ✅ `ModelRepository.models` StateFlow for reactive model data
- ✅ `SettingsRepository.settings` StateFlow for reactive settings data
- ✅ `SettingsRepository.loadSettingsByModelId()` for model-specific settings
- ✅ `SessionRepository.updateSessionModel()` and `updateSessionSettings()`

### 4. ViewModel Layer Enhancement (Implementation Required)

**Goal:** Extend `ChatState` and `ChatStateImpl` to expose available models and settings for UI selection.

#### 4.1 Update ChatState Interface

**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatState.kt`

Add new StateFlow properties:
```kotlin
/**
 * The list of all currently configured LLM models available for selection.
 */
val availableModels: StateFlow<DataState<RepositoryError, List<LLMModel>>>

/**
 * The list of settings profiles available for the currently selected model.
 */
val availableSettingsForCurrentModel: StateFlow<DataState<RepositoryError, List<ModelSettings>>>
```

#### 4.2 Implement in ChatStateImpl

**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatStateImpl.kt`

Implement the new StateFlows:
```kotlin
// Expose all available models directly from ModelRepository
override val availableModels: StateFlow<DataState<RepositoryError, List<LLMModel>>> =
    modelRepository.models.stateIn(
        scope = backgroundScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = DataState.Idle
    )

// Settings filtered by current model
override val availableSettingsForCurrentModel: StateFlow<DataState<RepositoryError, List<ModelSettings>>> =
    combine(
        sessionDataState,
        settingsRepository.settings
    ) { sessionState, settingsState ->
        val currentModelId = sessionState.dataOrNull?.session?.currentModelId
        when (settingsState) {
            is DataState.Success -> {
                val filteredSettings = if (currentModelId != null) {
                    settingsState.data.filter { it.modelId == currentModelId }
                } else {
                    emptyList()
                }
                DataState.Success(filteredSettings)
            }
            is DataState.Error -> settingsState
            is DataState.Loading -> DataState.Loading
            is DataState.Idle -> DataState.Idle
        }
    }.stateIn(
        scope = backgroundScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = DataState.Idle
    )
```

### 5. UI State Enhancement (Implementation Required)

#### 5.1 Update ChatAreaState

**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatAreaState.kt`

Add properties for model/settings selection:
```kotlin
data class ChatAreaState(
    // ... existing properties ...
    val availableModels: DataState<RepositoryError, List<LLMModel>> = DataState.Idle,
    val availableSettingsForCurrentModel: DataState<RepositoryError, List<ModelSettings>> = DataState.Idle
)
```

#### 5.2 Update ChatScreen State Mapping

**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatScreen.kt`

Update the ChatAreaState construction to include the new properties:
```kotlin
val chatAreaState = remember(
    // ... existing dependencies ...
    chatAvailableModels, chatAvailableSettings
) {
    ChatAreaState(
        // ... existing properties ...
        availableModels = chatAvailableModels,
        availableSettingsForCurrentModel = chatAvailableSettings
    )
}
```

### 6. UI Component Implementation (Implementation Required)

#### 6.1 Create ModelSettingsSelector Component

**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ModelSettingsSelector.kt`

Create a new Composable for model/settings selection:
```kotlin
@Composable
fun ModelSettingsSelector(
    currentModel: LLMModel?,
    currentSettings: ModelSettings?,
    availableModels: DataState<RepositoryError, List<LLMModel>>,
    availableSettings: DataState<RepositoryError, List<ModelSettings>>,
    onSelectModel: (Long?) -> Unit,
    onSelectSettings: (Long?) -> Unit,
    modifier: Modifier = Modifier
) {
    // Implementation with dropdowns/dialogs for selection
    // Handle loading and error states
    // Display current selections
}
```

#### 6.2 Integrate into ChatArea

**File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatArea.kt`

Update `SuccessStateDisplay` to include the selector:
```kotlin
Column(modifier = Modifier.fillMaxSize()) {
    // Add ModelSettingsSelector above MessageList
    ModelSettingsSelector(
        currentModel = chatSessionData.llmModel,
        currentSettings = chatSessionData.modelSettings,
        availableModels = state.availableModels,
        availableSettingsForCurrentModel = state.availableSettingsForCurrentModel,
        onSelectModel = actions::onSelectModel,
        onSelectSettings = actions::onSelectSettings,
        modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp, vertical = 8.dp)
    )
    
    MessageList(/* ... */)
    InputArea(/* ... */)
}
```

### 7. Data Loading Enhancement (Implementation Required)

#### 7.1 Ensure Data Loading in ChatViewModel

The ChatViewModel should trigger loading of models and settings when a session becomes active:

```kotlin
// In ChatViewModel or appropriate use case
fun loadSessionData(sessionId: Long) {
    normalScope.launch {
        // Load session details (already implemented)
        loadSessionUC.execute(sessionId)
        
        // Ensure models and settings are loaded
        parZip(
            { modelRepository.loadModels() },
            { settingsRepository.loadSettings() }
        ) { modelsResult, settingsResult ->
            // Handle results (errors already handled by repositories)
        }
    }
}
```

### 8. Testing Requirements

#### 8.1 Unit Tests
- **ChatStateImpl**: Test new StateFlows emit correct values
- **ModelSettingsSelector**: Test UI states and interactions
- **Integration**: Test model/settings selection flow end-to-end

#### 8.2 UI Tests
- **Preview Composables**: Create previews for different states
- **Interaction Tests**: Verify dropdown/dialog functionality

### 9. Implementation Order

1. **Update ChatState interface** (Step 4.1)
2. **Implement ChatStateImpl flows** (Step 4.2)  
3. **Update ChatAreaState** (Step 5.1)
4. **Create ModelSettingsSelector component** (Step 6.1)
5. **Integrate selector into ChatArea** (Step 6.2)
6. **Update ChatScreen state mapping** (Step 5.2)
7. **Add data loading enhancement** (Step 7.1)
8. **Add tests** (Step 8)

### 10. Notes

- **Reactive Architecture**: The implementation leverages the existing reactive architecture where state flows automatically update when underlying data changes.
- **Error Handling**: All error handling is already implemented in the repository layer and will be automatically propagated to the UI.
- **Performance**: Using `WhileSubscribed` with timeout ensures efficient resource usage.
- **Backwards Compatibility**: All changes are additive and don't break existing functionality.
