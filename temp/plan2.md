# Development Plan: Display "Unsupported Model Type" Error with ErrorNotifier

## Overview
This plan details how to replace the current `println()` statement with a proper error notification system using the existing ErrorNotifier infrastructure. The current code prints "Cannot add new settings: Model type ${selectedModel.type} is not supported." to the console, but this should be displayed to the user via the UI error notification system.

## Current State Analysis

### Existing Error Handling System
- **ErrorNotifier**: Centralized error handling service that uses EventBus to emit error events
- **Supported Error Types**: Currently handles API errors (`ApiRequestError`) and Repository errors (`RepositoryAppError`)
- **Missing**: No support for generic business logic errors like "unsupported model type"
- **Event System**: Uses EventBus to emit error events that get displayed in the UI

### Current SettingsConfigViewModel State
- **Location**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/SettingsConfigViewModel.kt`
- **Issue Line**: Line 173 - `println("Cannot add new settings: Model type ${selectedModel.type} is not supported.")`
- **Dependencies**: Currently only has `SettingsRepository` and `ModelRepository`
- **Missing**: No `ErrorNotifier` or `EventBus` dependency injection

## Development Plan

### Phase 1: Extend Error Handling System

#### 1.1 Add Generic Error Support to ErrorNotifier
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/common/ErrorNotifier.kt`

**Changes needed**:
```kotlin
/**
 * Handles a generic application error by creating a standardized error event and emitting it.
 * Used for business logic errors, validation errors, and other application-specific errors.
 *
 * @param shortMessage The short, user-friendly error message (can be internationalized)
 * @param detailedMessage Optional detailed technical error message in English for developers
 * @param isRetryable Whether this error can be retried
 * @return The event ID of the emitted error event
 */
suspend fun genericError(
    shortMessage: String,
    detailedMessage: String? = null,
    isRetryable: Boolean = false
): String {
    logger.warn("Generic error: $shortMessage")
    val errorEvent = GenericAppError(
        originalThrowable = null,
        message = buildString {
            append(shortMessage)
            if (detailedMessage != null) {
                append("\n\nTechnical details: ")
                append(detailedMessage)
            }
        },
        isRetryable = isRetryable
    )
    eventBus.emitEvent(errorEvent)
    return errorEvent.eventId
}

/**
 * Handles a generic application error using optional string resources for the short message.
 * The detailed message is always in English for technical purposes.
 *
 * @param shortMessageRes Optional string resource for the short, user-friendly message
 * @param shortMessage Fallback short message if no resource provided
 * @param detailedMessage Optional detailed technical error message in English for developers
 * @param isRetryable Whether this error can be retried
 * @return The event ID of the emitted error event
 */
suspend fun genericError(
    shortMessageRes: StringResource? = null,
    shortMessage: String? = null,
    detailedMessage: String? = null,
    isRetryable: Boolean = false
): String {
    val resolvedShortMessage = when {
        shortMessageRes != null -> getString(shortMessageRes)
        shortMessage != null -> shortMessage
        else -> "An error occurred" // Fallback
    }
    return genericError(resolvedShortMessage, detailedMessage, isRetryable)
}
```

**Rationale**: 
- Reuse existing `GenericAppError` class for generic errors
- Follow the same pattern as existing error methods
- Support both internationalized short messages and English technical details
- Both messages are displayed together for better user experience and developer support
- Flexible overloads for different use cases

#### 1.2 Add String Resources for Unsupported Model Type Error
**File**: `app/src/commonMain/composeResources/values/strings.xml`

**Changes needed**:
```xml
<string name="error_unsupported_model_type">Unsupported model type</string>
```

**Rationale**:
- Only the short message needs internationalization
- Keep it simple and generic for reusability
- Detailed technical message stays in English

### Phase 2: Update Dependency Injection

#### 2.1 Update Koin Configuration
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt`

**Current**:
```kotlin
viewModel { SettingsConfigViewModel(get<SettingsRepository>(), get<ModelRepository>()) }
```

**Change to**:
```kotlin
viewModel { SettingsConfigViewModel(get<SettingsRepository>(), get<ModelRepository>(), get<ErrorNotifier>()) }
```

**Rationale**: Add ErrorNotifier dependency to enable error notifications in SettingsConfigViewModel.

### Phase 3: Update SettingsConfigViewModel

#### 3.1 Add ErrorNotifier Dependency
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/SettingsConfigViewModel.kt`

**Changes needed**:

1. **Import statements**:
```kotlin
import eu.torvian.chatbot.app.viewmodel.common.ErrorNotifier
```

2. **Constructor parameters**:
```kotlin
class SettingsConfigViewModel(
    private val settingsRepository: SettingsRepository,
    private val modelRepository: ModelRepository,
    private val errorNotifier: ErrorNotifier, // Add this parameter
    private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main
) : ViewModel() {
```

3. **Replace println with error notification**:

**Current (line 173)**:
```kotlin
println("Cannot add new settings: Model type ${selectedModel.type} is not supported.")
```

**Replace with**:
```kotlin
viewModelScope.launch {
    errorNotifier.genericError(
        shortMessageRes = Res.string.error_unsupported_model_type,
        detailedMessage = "Cannot add new settings: Model type ${selectedModel.type} is not supported.",
        isRetryable = false
    )
}
```

**Rationale**:
- Use coroutine scope since ErrorNotifier methods are suspend functions
- Short message is internationalized for user understanding
- Detailed message in English provides technical context for developers
- Mark as non-retryable since it's a business logic constraint
- Both messages will be displayed together in the UI

#### 3.2 Alternative Implementation Without String Resources
**If string resources are not desired**:
```kotlin
viewModelScope.launch {
    errorNotifier.genericError(
        shortMessage = "Unsupported model type",
        detailedMessage = "Cannot add new settings: Model type ${selectedModel.type} is not supported.",
        isRetryable = false
    )
}
```

### Phase 4: Testing and Validation

#### 4.1 Unit Tests
**File**: `app/src/commonTest/kotlin/eu/torvian/chatbot/app/viewmodel/SettingsConfigViewModelTest.kt`

**Test cases needed**:
1. Test that `startAddingNewSettings()` calls `errorNotifier.genericError()` when model type is unsupported
2. Verify that the correct short and detailed messages are passed
3. Ensure the method returns early and doesn't proceed with dialog state change
4. Mock ErrorNotifier and verify interaction
5. Test both string resource and direct string implementations

#### 4.2 Integration Tests
1. Verify that error events are properly emitted to EventBus
2. Test that UI properly displays both short and detailed error messages
3. Verify error message formatting with different model types
4. Test internationalization of short messages

#### 4.3 Manual Testing Scenarios
1. **Setup**: Configure an unsupported model type in the database
2. **Action**: Select the unsupported model and try to add new settings
3. **Expected**: Error notification appears in UI with both short and detailed messages
4. **Verify**: Check that no dialog appears for adding new settings
5. **Internationalization**: Test with different language settings to verify short message translation

## Implementation Order

1. **Step 1**: Add `genericError` methods to ErrorNotifier class
2. **Step 2**: Add string resources for the short error message (optional)
3. **Step 3**: Update Koin dependency injection configuration
4. **Step 4**: Update SettingsConfigViewModel constructor and import
5. **Step 5**: Replace println statement with errorNotifier call
6. **Step 6**: Write and run unit tests
7. **Step 7**: Perform integration and manual testing

## Risk Assessment

### Low Risk
- **ErrorNotifier Extension**: Following established patterns, minimal risk
- **Dependency Injection**: Simple parameter addition, well-established pattern
- **Generic Error Approach**: More flexible and reusable than validation-specific

### Medium Risk
- **Message Formatting**: Need to ensure both short and detailed messages display properly in UI
- **Coroutine Context**: ErrorNotifier calls are suspend functions, need proper scope

### Mitigation Strategies
1. **Message Display**: Test UI rendering of combined messages thoroughly
2. **Error Handling**: Wrap errorNotifier call in try-catch if needed
3. **Testing**: Comprehensive unit tests before integration

## Success Criteria

1. ✅ Console output is eliminated - no more println statements
2. ✅ Error appears in UI notification system consistently
3. ✅ Both short (internationalized) and detailed (English) messages are displayed
4. ✅ No regression in existing functionality
5. ✅ Code follows established patterns and conventions
6. ✅ Proper dependency injection and testability maintained
7. ✅ Short message can be internationalized while detailed message remains in English

## Future Enhancements

1. **Categorized Generic Errors**: Create specific generic error types if needed
2. **User Actions**: Add "Learn More" or "Configure Supported Models" actions
3. **Analytics**: Track generic error occurrences for product insights
4. **Batch Error Handling**: Extend pattern to other generic error scenarios in ViewModels
5. **Developer Tools**: Add option to show/hide detailed technical messages based on user preference

## Dependencies

- **No external dependencies**: Uses existing ErrorNotifier and EventBus infrastructure
- **No breaking changes**: Purely additive changes to existing APIs
- **Backward compatible**: Existing error handling continues to work unchanged
- **Internationalization ready**: Supports both localized user messages and English technical details
