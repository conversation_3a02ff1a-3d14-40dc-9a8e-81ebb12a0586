This development plan outlines the steps required to implement the "Allow user to select the current model and settings profile for the next assistant response" feature (E4.S7), building upon your existing project structure and planned PRs.

The good news is that much of the groundwork, especially in the `ChatViewModel` and `usecase` layer, is already present or explicitly planned!

---

## Development Plan: E4.S7 - Select Model & Settings Profile for Session

**Feature:** Allow user to select the current model and settings profile for the next assistant response.
**User Story ID:** E4.S7
**Assigned To:** <PERSON> (Frontend UI/ViewModel), <PERSON> (Backend/API Review/Completion)
**Estimated Effort:** Medium

### 1. Backend API & Service Completion (Eric/Alex - Review/Finish)

**Goal:** Ensure the backend API endpoints and underlying services/DAOs are fully functional for retrieving all models, settings, and updating session's `currentModelId` and `currentSettingsId`.

*   **Review `PR 9: Wire Backend API - Session & Group Management` & `PR 10: Wire Backend API - Config (Providers, Models, Settings)`:**
    *   **`PR 9` Check:** Verify that the `PUT /api/v1/sessions/{sessionId}/model` and `PUT /api/v1/sessions/{sessionId}/settings` (or a single combined `PATCH /api/v1/sessions/{sessionId}` route) are fully implemented and wired to `SessionService.updateSessionModel` and `SessionService.updateSessionSettings` respectively. The current `SelectModelUseCase` and `SelectSettingsUseCase` imply these services exist.
    *   **`PR 10` Check:** Confirm that `GET /api/v1/models` (to get all LLM models) and `GET /api/v1/models/{modelId}/settings` (to get all settings profiles for a specific model) are fully implemented and wired to `LLMModelService` and `ModelSettingsService` respectively.
*   **Database Schema (`E7.S4` Check):** Ensure `ChatSessionTable` correctly defines `currentModelId` and `currentSettingsId` as nullable foreign keys referencing `LLMModelTable` and `ModelSettingsTable` with `ON DELETE SET NULL` cascades. This is crucial for data integrity.

### 2. Frontend API Clients (Maya - Review/Finish)

**Goal:** Ensure the frontend API client methods are available for fetching models, settings, and updating session configurations.

*   **Review `PR 12: Implement Frontend Chat & Session API Clients`:**
    *   Confirm `KtorSessionApiClient` contains `updateSessionModel` and `updateSessionSettings` methods that correctly call the backend `PUT` routes from step 1.
*   **Review `PR 13: Implement Frontend Group & Config API Clients`:**
    *   Confirm `KtorModelApiClient` contains a `getAllModels` method.
    *   Confirm `KtorSettingsApiClient` contains a `getSettingsByModelId` method.

### 3. Frontend ViewModel & State Augmentation (Maya)

**Goal:** Extend `ChatState` and `ChatStateImpl` to expose the necessary reactive data for *all* available models and settings, not just the currently selected ones.

*   **Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatState.kt`:**
    *   Add new `StateFlow` properties to expose lists of available models and settings.
    ```kotlin
    interface ChatState {
        // ... existing properties ...

        /**
         * The list of all currently configured LLM models available for selection.
         */
        val availableModels: StateFlow<DataState<RepositoryError, List<LLMModel>>>

        /**
         * The list of settings profiles available for the currently selected model.
         */
        val availableSettingsForCurrentModel: StateFlow<DataState<RepositoryError, List<ModelSettings>>>

        // ... existing methods ...
    }
    ```
*   **Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/state/ChatStateImpl.kt`:**
    *   **Inject Repositories:** Ensure `ModelRepository` and `SettingsRepository` are injected into `ChatStateImpl`'s constructor if they aren't already. (They are, based on `llmModelForActiveSession` and `modelSettingsForActiveSession`).
    *   **Implement `availableModels` Flow:**
        *   Create a `StateFlow` that fetches all models from `modelRepository.getAllModelsFlow()`. This should ideally be `started = SharingStarted.Eagerly` or `WhileSubscribed` to be always available.
        ```kotlin
        // In ChatStateImpl
        override val availableModels: StateFlow<DataState<RepositoryError, List<LLMModel>>> =
            modelRepository.getAllModelsFlow()
                .stateIn(
                    scope = backgroundScope,
                    started = SharingStarted.WhileSubscribed(5000), // Adjust timeout as needed
                    initialValue = DataState.Loading // Initial state while fetching
                )
        ```
    *   **Implement `availableSettingsForCurrentModel` Flow:**
        *   This flow needs to react to changes in the *currently selected model* (`sessionDataState.dataOrNull?.llmModel?.id`).
        *   It will then call `settingsRepository.getSettingsByModelIdFlow(modelId)`.
        ```kotlin
        // In ChatStateImpl
        override val availableSettingsForCurrentModel: StateFlow<DataState<RepositoryError, List<ModelSettings>>> =
            sessionDataState
                .flatMapLatest { sessionDataState ->
                    val modelId = (sessionDataState.dataOrNull?.llmModel as? LLMModel)?.id
                    if (modelId == null) {
                        flowOf(DataState.Success(emptyList())) // No model selected, no settings
                    } else {
                        settingsRepository.getSettingsByModelIdFlow(modelId)
                    }
                }
                .stateIn(
                    scope = backgroundScope,
                    started = SharingStarted.WhileSubscribed(5000), // Adjust timeout as needed
                    initialValue = DataState.Idle
                )
        ```
*   **Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatAreaState.kt`:**
    *   Add corresponding properties to `ChatAreaState` to mirror the new flows in `ChatState`.
    ```kotlin
    data class ChatAreaState(
        // ... existing properties ...
        val availableModels: DataState<RepositoryError, List<LLMModel>> = DataState.Idle,
        val availableSettingsForCurrentModel: DataState<RepositoryError, List<ModelSettings>> = DataState.Idle
    )
    ```
*   **Update `ChatViewModel` (Dependency Injection):**
    *   No direct changes needed in `ChatViewModel.kt` itself for *exposing* these states, as it will delegate to the `ChatState` interface. However, ensure `ModelRepository` and `SettingsRepository` are correctly provided via Koin to `ChatStateImpl`.

### 4. Frontend UI Implementation (Maya)

**Goal:** Create a new Composable for model/settings selection and integrate it into `ChatArea.kt`.

*   **Create `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ModelSettingsSelector.kt`:**
    *   This Composable will take:
        *   `currentModel: LLMModel?`
        *   `currentSettings: ModelSettings?`
        *   `availableModels: DataState<RepositoryError, List<LLMModel>>`
        *   `availableSettings: DataState<RepositoryError, List<ModelSettings>>`
        *   `onSelectModel: (Long?) -> Unit`
        *   `onSelectSettings: (Long?) -> Unit`
    *   It will render:
        *   A display of the currently selected model/settings.
        *   A dropdown or dialog to select from `availableModels`.
        *   A dropdown or dialog to select from `availableSettings` (filtered by the currently selected `LLMModel.type` if necessary, or ensure `availableSettings` only contains settings for `currentModel`).
        *   Handle loading and error states for `availableModels` and `availableSettings`.
*   **Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatArea.kt`:**
    *   **Integrate `ModelSettingsSelector`:**
        *   In `SuccessStateDisplay`, pass the `currentModelId` and `currentSettingsId` from `chatSession`, and the new `availableModels`/`availableSettingsForCurrentModel` from `state`.
        *   Place the `ModelSettingsSelector` Composable, likely above the `MessageList` or `InputArea`, or in a new `ChatHeader` Composable that `SuccessStateDisplay` uses.
        *   Wire `actions.onSelectModel` and `actions.onSelectSettings` to the selector.

    ```kotlin
    // Inside SuccessStateDisplay in ChatArea.kt
    Column(modifier = Modifier.fillMaxSize()) {
        // New: Model and Settings Selector
        ModelSettingsSelector(
            currentModel = chatSessionData.llmModel, // From ChatSessionData (renamed for clarity)
            currentSettings = chatSessionData.modelSettings, // From ChatSessionData
            availableModels = state.availableModels,
            availableSettingsForCurrentModel = state.availableSettingsForCurrentModel,
            onSelectModel = actions::onSelectModel,
            onSelectSettings = actions::onSelectSettings,
            modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp, vertical = 8.dp)
        )
        MessageList( /* ... */ )
        InputArea( /* ... */ )
    }
    ```
    *   **Update `SuccessStateDisplay` parameters:** The current `SuccessStateDisplay` takes `chatSession: ChatSession`. To get `llmModel` and `modelSettings`, it should probably take `chatSessionData: ChatSessionData` directly. This change is consistent with `ChatAreaState`.

### 5. Wiring UI to ViewModel (Maya)

**Goal:** Connect the `ChatArea`'s `ChatAreaActions` to the `ChatViewModel`'s public functions.

*   **Review `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/chat/ChatViewModel.kt`:**
    *   Confirm `selectModel(modelId: Long?)` and `selectSettings(settingsId: Long?)` exist and correctly call their respective use cases. (They do, based on provided code).
*   **Review `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/chatarea/ChatAreaActions.kt`:**
    *   Confirm `onSelectModel(modelId: Long?)` and `onSelectSettings(settingsId: Long?)` exist. (They do, based on provided code).
*   **Wire in `ChatScreen.kt` (or wherever `ChatArea` is instantiated):**
    *   Ensure the `ChatAreaActions` implementation passed to `ChatArea` correctly delegates `onSelectModel` and `onSelectSettings` to `chatViewModel.selectModel` and `chatViewModel.selectSettings`.

### 6. Testing (Maya, Eric/Alex)

*   **Unit Tests for `ChatStateImpl`:**
    *   Verify that `availableModels` and `availableSettingsForCurrentModel` flows emit correct `DataState` values (Loading, Success(data), Error) when repositories return different results.
    *   Test that `availableSettingsForCurrentModel` correctly reacts to changes in `activeSessionId` and the `currentModelId` within the session.
*   **Unit Tests for `ChatViewModel`:**
    *   Verify `selectModel` and `selectSettings` functions trigger the correct use cases.
*   **Integration Tests (Frontend `KtorChatApiClient`):**
    *   Test `updateSessionModel` and `updateSessionSettings` against the live backend (or a mock server) to ensure correct API calls and deserialization.
*   **UI Tests (Compose Desktop):**
    *   Write `@Preview` Composables for `ModelSettingsSelector` to test its various states (loading, error, model selected, settings selected, no selection).
    *   Write UI tests for `ChatArea` to ensure the selector appears, can be interacted with, and updates the displayed session data.

---
This plan breaks down E4.S7 into manageable tasks across the different layers of your application, leveraging your existing architecture and planned PRs.




### E4.S7 - Select Model & Settings Profile for Session
*   **Description:** As a user, within an active chat session, I want to easily choose which configured LLM Model and specific settings profile will be used for the next assistant response, allowing me to change the AI's approach mid-conversation.
*   **Estimate:** M
*   **Acceptance Criteria:**
    *   UI element (e.g., dropdown) shows currently selected model/settings for the session.
    *   Clicking element displays list of configured models (from E4.S2) and their settings profiles (from E4.S5).
    *   Selecting a different model/settings updates UI element.
    *   Frontend calls backend `updateSessionDetails` to update `currentModelId` and `currentSettingsId` for the `ChatSession`.
    *   (Dependency E1.S4): Backend LLM logic uses these IDs to retrieve model/settings before calling LLM.
    *   (Cross-cutting E2.S2): Updated session record is persisted.
    *   (Cross-cutting E7.S6): Database update is asynchronous.

---